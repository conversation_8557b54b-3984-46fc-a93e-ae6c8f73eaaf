#!/usr/bin/env python3
import asyncio
import argparse
import sys
import logging
import json
import os
import base64
import tempfile
import time
from playwright.async_api import async_playwright, Playwright
import app.services.screenshot.user_agents as ua

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(levelname)s: %(message)s',
    stream=sys.stdout
)

logger = logging.getLogger("screenshot_driver")

# Social media credentials for login
SOCIAL_MEDIA_USERNAME = "<EMAIL>"
SOCIAL_MEDIA_PASSWORD = "Biztelsocial@1802"

# Use same credentials for both Instagram and Facebook
INSTAGRAM_USERNAME = SOCIAL_MEDIA_USERNAME
INSTAGRAM_PASSWORD = SOCIAL_MEDIA_PASSWORD
FACEBOOK_USERNAME = SOCIAL_MEDIA_USERNAME
FACEBOOK_PASSWORD = SOCIAL_MEDIA_PASSWORD

def normalize_instagram_url(url):
    """
    Normalize Instagram URLs to avoid login redirects
    """
    if "instagram.com" in url:
        # Remove any tracking parameters
        if "?" in url:
            url = url.split("?")[0]
        
        # Ensure URL ends with / for consistency
        if not url.endswith("/"):
            url += "/"
            
        # For specific post URLs, try to keep them as-is
        # For profile URLs, ensure they're properly formatted
        if "/p/" in url or "/reel/" in url or "/tv/" in url:
            # This is a specific content URL, keep as-is
            pass
        elif url.endswith("instagram.com/"):
            # Home page, keep as-is
            pass
        else:
            # This might be a profile URL, ensure proper format
            parts = url.split("/")
            if len(parts) >= 4 and parts[3]:  # Has username
                # Reconstruct URL properly
                username = parts[3]
                url = f"https://www.instagram.com/{username}/"
    
    return url

async def handle_instagram_login_redirect(page, target_url):
    """
    Handle Instagram login when redirected to login page during screenshot capture

    Args:
        page: Playwright page object
        target_url: The original URL we want to access

    Returns:
        bool: True if login successful and navigated to target, False otherwise
    """
    logger.info("Handling Instagram login redirect...")

    try:
        # Check if we're actually on a login page
        current_url = page.url
        if "/accounts/login" not in current_url and "/login" not in current_url:
            logger.info("Not on login page, no login needed")
            return True

        # Check for existing session first
        try:
            # Look for any sign we might already be logged in
            logged_in_indicators = [
                'nav[role="navigation"]',
                'svg[aria-label="Home"]',
                'a[href="/direct/inbox/"]'
            ]

            for indicator in logged_in_indicators:
                if await page.locator(indicator).count() > 0:
                    logger.info("Already logged in, navigating to target URL")
                    await page.goto(target_url, timeout=30000)
                    return True
        except:
            pass

        # Perform login
        logger.info("Performing Instagram login...")

        # Wait for login form elements
        await page.wait_for_selector('input[name="username"]', timeout=10000)

        # Fill credentials
        await page.fill('input[name="username"]', INSTAGRAM_USERNAME)
        await page.wait_for_timeout(500)
        await page.fill('input[name="password"]', INSTAGRAM_PASSWORD)
        await page.wait_for_timeout(500)

        # Submit form
        await page.click('button[type="submit"]')
        await page.wait_for_timeout(3000)

        # Wait for login to complete (URL should change)
        try:
            await page.wait_for_function(
                "() => !window.location.pathname.includes('/accounts/login')",
                timeout=15000
            )
        except:
            logger.warning("Login may have failed - still on login page")
            return False

        # Check for 2FA or challenges
        current_url = page.url
        if "challenge" in current_url or "two_factor" in current_url:
            logger.warning("Instagram requires 2FA/challenge - cannot proceed automatically")
            return False

        # Verify login success
        await page.wait_for_timeout(2000)

        # Try to navigate to the target URL
        logger.info(f"Login completed, navigating to target URL: {target_url}")
        await page.goto(target_url, timeout=30000)
        await page.wait_for_timeout(3000)

        # Check if we're still on a login page
        final_url = page.url
        if "/accounts/login" in final_url or "/login" in final_url:
            logger.warning("Still on login page after login attempt")
            return False

        logger.info("Instagram login and navigation successful")
        return True

    except Exception as e:
        logger.error(f"Instagram login failed: {str(e)}")
        return False

async def handle_facebook_login_redirect(page, target_url):
    """
    Handle Facebook login when redirected to login page during screenshot capture

    Args:
        page: Playwright page object
        target_url: The original URL we want to access

    Returns:
        bool: True if login successful and navigated to target, False otherwise
    """
    logger.info("Handling Facebook login redirect...")

    try:
        # Check if we're actually on a login page
        current_url = page.url
        if "/login" not in current_url and "facebook.com/login" not in current_url:
            logger.info("Not on Facebook login page, no login needed")
            return True

        # Check for existing session first
        try:
            # Look for any sign we might already be logged in
            logged_in_indicators = [
                'image[alt*="profile picture"]',
                '[aria-label="Your profile"]',
                '[data-testid="blue_bar"]',
                'div[role="banner"]'
            ]

            for indicator in logged_in_indicators:
                if await page.locator(indicator).count() > 0:
                    logger.info("Already logged in to Facebook, navigating to target URL")
                    await page.goto(target_url, timeout=30000)
                    return True
        except:
            pass

        # Perform login
        logger.info("Performing Facebook login...")

        # Wait for login form elements
        await page.wait_for_selector('input[name="email"]', timeout=10000)

        # Fill credentials
        await page.fill('input[name="email"]', FACEBOOK_USERNAME)
        await page.wait_for_timeout(500)
        await page.fill('input[name="pass"]', FACEBOOK_PASSWORD)
        await page.wait_for_timeout(500)

        # Submit form
        await page.click('button[name="login"]')
        await page.wait_for_timeout(3000)

        # Wait for login to complete (URL should change)
        try:
            await page.wait_for_function(
                "() => !window.location.pathname.includes('/login')",
                timeout=15000
            )
        except:
            logger.warning("Login may have failed - still on login page")
            return False

        # Handle post-login redirects
        current_url = page.url
        if "sk=welcome" in current_url:
            logger.info("Detected Facebook welcome screen, redirecting to homepage...")
            await page.goto("https://www.facebook.com/", timeout=30000)
            await page.wait_for_timeout(3000)

        # Check for 2FA or security checks
        if "checkpoint" in current_url or "two_factor" in current_url:
            logger.warning("Facebook requires additional verification (2FA/checkpoint)")
            logger.warning("Please complete verification manually or use an account without 2FA")
            return False

        # Verify login success
        await page.wait_for_timeout(2000)

        # Try to navigate to the target URL
        logger.info(f"Login completed, navigating to target URL: {target_url}")
        await page.goto(target_url, timeout=30000)
        await page.wait_for_timeout(3000)

        # Check if we're still on a login page
        final_url = page.url
        if "/login" in final_url:
            logger.warning("Still on login page after login attempt")
            return False

        # Verify we have logged-in indicators
        try:
            logged_in_selectors = [
                'image[alt*="profile picture"]',
                '[aria-label="Your profile"]',
                '[data-testid="blue_bar"]'
            ]

            login_success = False
            for selector in logged_in_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    login_success = True
                    logger.info(f"Facebook login verified with selector: {selector}")
                    break
                except:
                    continue

            if login_success:
                logger.info("Facebook login and navigation successful")
                return True
            else:
                logger.warning("Login appeared to complete but couldn't verify logged-in state")
                return False

        except Exception as e:
            logger.warning(f"Error verifying Facebook login: {str(e)}")
            return False

    except Exception as e:
        logger.error(f"Facebook login failed: {str(e)}")
        return False

async def capture_instagram_screenshot_fallback(playwright: Playwright, url, timeout=60):
    """
    Fallback method for Instagram screenshots using a different approach
    """
    logger.info(f"Attempting Instagram fallback screenshot method for: {url}")
    
    # Try Firefox as an alternative to Chrome
    browser = await playwright.firefox.launch(
        headless=True,
        args=['--no-sandbox', '--disable-setuid-sandbox']
    )
    
    context = await browser.new_context(
        viewport={"width": 1366, "height": 768},
        user_agent= ua.get_random_user_agent(),
        extra_http_headers={
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1"
        }
    )
    
    page = await context.new_page()
    
    try:
        # Navigate with a shorter timeout and different strategy
        await page.goto(url, wait_until="load", timeout=timeout*1000)

        # Wait for any content to load
        await page.wait_for_timeout(3000)  # Increased from 2000 to 3000ms for better content loading

        # Try to dismiss any popups quickly
        try:
            await page.keyboard.press('Escape')
            await page.wait_for_timeout(1000)  # Increased from 500 to 1000ms
        except Exception:
            pass

        # Wait for images to load (Instagram-specific timing)
        logger.info("Waiting for Instagram images to load...")
        await page.wait_for_timeout(5000)  # 5 seconds for Instagram image loading

        # Try to wait for network to settle
        try:
            await page.wait_for_load_state("networkidle", timeout=3000)
            logger.info("Network settled for Instagram fallback")
        except Exception:
            logger.info("Network settle timeout in Instagram fallback (expected)")

        # Take screenshot
        screenshot_data = await page.screenshot(full_page=True, type="png")
        return screenshot_data

    except Exception as e:
        logger.warning(f"Firefox fallback failed: {str(e)}")
        return None
    finally:
        # Robust cleanup
        try:
            contexts = browser.contexts
            for context in contexts:
                try:
                    await context.close()
                except Exception:
                    pass
            await browser.close()
        except Exception:
            pass

async def capture_screenshot(playwright: Playwright, url, timeout=60, close_popups=True, wait_after_close=100, image_load_wait=3000):
    """
    Capture a screenshot of a URL using Playwright - OPTIMIZED VERSION

    Args:
        playwright (Playwright): The Playwright instance.
        url (str): The URL to capture a screenshot of
        timeout (int): Timeout in seconds (reduced from 90 to 30)
        close_popups (bool): Whether to attempt closing popups
        wait_after_close (int): Time in milliseconds to wait after closing popups (reduced from 500 to 100)
        image_load_wait (int): Time in milliseconds to wait for images to load before screenshot (default: 3000)

    Returns:
        bytes: Screenshot data as bytes
    """
    start_time = time.time()
    
    # Normalize Instagram URLs to avoid login redirects
    original_url = url
    url = normalize_instagram_url(url)
    if url != original_url:
        logger.info(f"Normalized URL: {original_url} -> {url}")
    
    logger.info(f"Starting OPTIMIZED screenshot capture for {url}")
    
    # Optimized browser launch with performance flags
    browser = await playwright.chromium.launch(
        headless=True,
        args=[
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--disable-gpu',
            '--window-size=1920,1080',
            '--disable-extensions',
            '--no-first-run',
        ]
    )
    # Enhanced context for Instagram with better stealth settings
    context_options = {
        "viewport": {"width": 1920, "height": 1080},
        "user_agent": ua.get_random_user_agent(),
        "extra_http_headers": {
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Cache-Control": "no-cache"
        }
    }
    
    # For Instagram, add some basic cookies to appear more like a real browser
    if "instagram.com" in url:
        context_options["extra_http_headers"]["Referer"] = "https://www.google.com/"
        context_options["extra_http_headers"]["Sec-Fetch-User"] = "?1"
    
    context = await browser.new_context(**context_options)
    page = await context.new_page()
    
    # Enhanced stealth scripts to avoid detection
    await page.add_init_script("""
        // Remove webdriver property
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // Override the plugins property to use a custom getter
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // Override the languages property to use a custom getter
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
        });
        
        // Override the permissions property to use a custom getter
        Object.defineProperty(navigator, 'permissions', {
            get: () => ({
                query: () => Promise.resolve({ state: 'granted' }),
            }),
        });
        
        // Mock chrome runtime for better stealth
        window.chrome = {
            runtime: {}
        };
        
        // Override screen resolution
        Object.defineProperty(screen, 'width', {
            get: () => 1920,
        });
        Object.defineProperty(screen, 'height', {
            get: () => 1080,
        });
    """)
    
    try:
        # Set faster timeouts for the page
        page.set_default_timeout(timeout * 1000)
        page.set_default_navigation_timeout(timeout * 1000)
        
        # Navigate to the page with optimized wait strategy
        logger.info(f"Navigating to {url}")
        await page.goto(url, wait_until="domcontentloaded", timeout=timeout*5000)  # Changed from networkidle to domcontentloaded
        
        if close_popups:
            # Enhanced popup detection timing - wait longer for popups to fully load
            logger.info("Waiting for popups to appear and load completely...")
            await asyncio.sleep(3)  # Increased from 2 to 3 seconds for better popup detection

            # Try to press Escape key as it often closes modal popups on many sites
            try:
                await page.keyboard.press('Escape')
                logger.info("Pressed Escape key to try closing popup")
                await page.wait_for_timeout(500)  # Increased from 200 to 500ms for better response time
            except Exception as e:
                logger.warning(f"Error pressing Escape key: {str(e)}")

            # Platform-specific popup handling with improved timing
            if "instagram.com" in url:
                logger.info("Handling Instagram-specific redirects and popups...")

                try:
                    # Strategy 1: Enhanced wait for initial page load and popup detection
                    await page.wait_for_timeout(4000)  # Increased from 3000 to 4000ms for better popup detection

                    # Strategy 2: Check for login redirect and try multiple approaches
                    current_url = page.url
                    logger.info(f"Current URL after initial load: {current_url}")

                    if "/accounts/login" in current_url or "/login" in current_url:
                        logger.info("Detected Instagram login page redirect - attempting login...")

                        # Use the enhanced login function
                        login_success = await handle_instagram_login_redirect(page, url)

                        if login_success:
                            logger.info("Instagram login successful, proceeding with screenshot")
                            current_url = page.url
                        else:
                            logger.warning("Instagram login failed, trying alternative approaches...")

                            # Fallback approach 1: Try adding parameters to bypass login
                            if "?" not in url:
                                alternative_url = url + "?__a=1"
                                try:
                                    await page.goto(alternative_url, wait_until="domcontentloaded", timeout=10000)
                                    await page.wait_for_timeout(1000)
                                    current_url = page.url
                                    logger.info(f"URL after parameter approach: {current_url}")
                                except Exception as e:
                                    logger.warning(f"Parameter approach failed: {str(e)}")

                            # Fallback approach 2: Try mobile version
                            if "/accounts/login" in current_url or "/login" in current_url:
                                mobile_url = url.replace("www.instagram.com", "m.instagram.com")
                                if mobile_url != url:
                                    logger.info("Trying mobile version...")
                                    try:
                                        await page.goto(mobile_url, wait_until="domcontentloaded", timeout=10000)
                                        await page.wait_for_timeout(1000)
                                        current_url = page.url
                                        logger.info(f"URL after mobile attempt: {current_url}")
                                    except Exception as e:
                                        logger.warning(f"Mobile version failed: {str(e)}")

                            # Fallback approach 3: Home-first navigation
                            if "/accounts/login" in current_url or "/login" in current_url:
                                logger.info("Trying home-first approach...")
                                try:
                                    await page.goto("https://www.instagram.com/", wait_until="domcontentloaded", timeout=10000)
                                    await page.wait_for_timeout(1000)

                                    # Try to dismiss any popups on home page
                                    try:
                                        await page.keyboard.press('Escape')
                                        await page.wait_for_timeout(500)
                                    except Exception:
                                        pass

                                    # Now try to navigate to the target URL
                                    if url != "https://www.instagram.com/":
                                        await page.goto(url, wait_until="domcontentloaded", timeout=10000)
                                        await page.wait_for_timeout(1000)
                                        current_url = page.url
                                        logger.info(f"URL after home-first attempt: {current_url}")
                                except Exception as e:
                                    logger.warning(f"Home-first approach failed: {str(e)}")
                    
                    # Strategy 3: Enhanced popup handling with better timing
                    await page.wait_for_timeout(1000)  # Increased from 500 to 1000ms for better popup detection

                    # Handle cookie consent banner with improved selectors
                    cookie_selectors = [
                        'button:has-text("Accept")',
                        'button:has-text("Allow all cookies")',
                        'button:has-text("Accept All")',
                        'button:has-text("Accept all")',
                        'button[data-cookiebanner="accept_button"]',
                        'button[data-testid="cookie-banner-accept"]',
                        '[role="button"]:has-text("Accept")',
                        'div[role="button"]:has-text("Accept")'
                    ]

                    for selector in cookie_selectors:
                        try:
                            cookie_btn = page.locator(selector)
                            if await cookie_btn.count() > 0:
                                await cookie_btn.first.click()
                                logger.info(f"Accepted cookies with selector: {selector}")
                                await page.wait_for_timeout(500)  # Increased from 200 to 500ms for better response
                                break
                        except Exception as e:
                            logger.debug(f"Cookie selector {selector} failed: {str(e)}")
                            continue
                    
                    # Handle notification popups with enhanced selectors
                    notification_selectors = [
                        'button:has-text("Not Now")',
                        'button:has-text("Not now")',
                        'button:has-text("Maybe Later")',
                        'button:has-text("Maybe later")',
                        'button:has-text("Later")',
                        'button:has-text("Skip")',
                        'button[data-testid="turnOnNotifications"]',
                        'button[data-testid="notNowButton"]',
                        '[role="button"]:has-text("Not Now")',
                        'div[role="button"]:has-text("Not Now")'
                    ]

                    for selector in notification_selectors:
                        try:
                            notif_btn = page.locator(selector)
                            if await notif_btn.count() > 0:
                                await notif_btn.first.click()
                                logger.info(f"Dismissed notification with selector: {selector}")
                                await page.wait_for_timeout(500)  # Increased from 200 to 500ms for better response
                                break
                        except Exception as e:
                            logger.debug(f"Notification selector {selector} failed: {str(e)}")
                            continue
                    
                    # Handle login/signup dialog overlays with enhanced selectors
                    close_selectors = [
                        'svg[aria-label="Close"][role="img"]',
                        'button[aria-label="Close"]',
                        'div[role="button"][aria-label="Close"]',
                        'button:has-text("Close")',
                        'button:has-text("×")',
                        'button:has-text("✕")',
                        '[data-testid="loginForm"] button[type="button"]',
                        '[data-testid="modal-close-button"]',
                        '.modal-close',
                        '[aria-label="Close dialog"]',
                        'button[title="Close"]'
                    ]

                    for selector in close_selectors:
                        try:
                            close_btn = page.locator(selector)
                            if await close_btn.count() > 0:
                                if selector == 'svg[aria-label="Close"][role="img"]':
                                    # For SVG icons, get the parent button
                                    button = close_btn.locator('xpath=ancestor::button').first
                                    await button.click()
                                else:
                                    await close_btn.first.click()
                                logger.info(f"Closed dialog with selector: {selector}")
                                await page.wait_for_timeout(500)  # Increased from 200 to 500ms for better response
                                break
                        except Exception as e:
                            logger.debug(f"Close selector {selector} failed: {str(e)}")
                            continue
                    
                    # Enhanced Escape key handling with better timing
                    for i in range(3):
                        try:
                            await page.keyboard.press('Escape')
                            await page.wait_for_timeout(500)  # Increased from 200 to 500ms for better response
                            logger.info(f"Pressed Escape key attempt {i+1}/3")
                        except Exception as e:
                            logger.debug(f"Escape key attempt {i+1} failed: {str(e)}")
                            break

                    # Strategy 4: Enhanced modal dialog handling
                    modal_dialogs = page.locator('div[role="dialog"], div[role="alertdialog"], .modal, .popup')
                    if await modal_dialogs.count() > 0:
                        logger.info(f"Found {await modal_dialogs.count()} modal dialogs, attempting to close...")

                        # Try clicking outside all modals with multiple positions
                        click_positions = [
                            {'x': 50, 'y': 50},
                            {'x': 100, 'y': 100},
                            {'x': 10, 'y': 10}
                        ]

                        for pos in click_positions:
                            try:
                                await page.click('body', position=pos)
                                await page.wait_for_timeout(300)
                                logger.info(f"Clicked outside modal at position {pos}")
                                break
                            except Exception as e:
                                logger.debug(f"Click outside modal at {pos} failed: {str(e)}")
                                continue

                        # Try clicking on backdrop with better targeting
                        try:
                            backdrop_selectors = ['.modal-backdrop', '.overlay', '[data-backdrop="true"]']
                            for backdrop_sel in backdrop_selectors:
                                backdrop = page.locator(backdrop_sel)
                                if await backdrop.count() > 0:
                                    await backdrop.first.click(position={'x': 10, 'y': 10})
                                    await page.wait_for_timeout(300)
                                    logger.info(f"Clicked backdrop with selector: {backdrop_sel}")
                                    break
                        except Exception as e:
                            logger.debug(f"Backdrop click failed: {str(e)}")
                            pass
                    
                    # Strategy 5: Final URL check and content optimization
                    final_url = page.url
                    logger.info(f"Final URL: {final_url}")

                    if "/accounts/login" in final_url or "/login" in final_url:
                        logger.warning("Still on Instagram login page after all attempts")
                        logger.warning("Screenshot will show login page - consider checking credentials or account status")

                        # Try one more login attempt if we haven't tried it yet
                        try:
                            login_retry_success = await handle_instagram_login_redirect(page, url)
                            if login_retry_success:
                                logger.info("Retry login successful")
                                final_url = page.url
                            else:
                                logger.warning("Retry login also failed")
                                # Try to scroll to see if there's any content beyond the login form
                                try:
                                    await page.mouse.wheel(0, 500)
                                    await page.wait_for_timeout(500)
                                    await page.mouse.wheel(0, -500)
                                    await page.wait_for_timeout(500)
                                except Exception:
                                    pass
                        except Exception as e:
                            logger.warning(f"Retry login attempt failed: {str(e)}")
                    else:
                        logger.info("Successfully navigated past Instagram login requirements")
                        # Wait for content to load
                        await page.wait_for_timeout(1000)
                    
                except Exception as e:
                    logger.warning(f"Error handling Instagram: {str(e)}")
            
            elif "facebook.com" in url:
                logger.info("Handling Facebook-specific popups and login with enhanced timing...")

                try:
                    # Wait for Facebook page to load
                    await page.wait_for_timeout(3000)

                    # Check if we're redirected to login page
                    current_url = page.url
                    if "/login" in current_url or "facebook.com/login" in current_url:
                        logger.info("Detected Facebook login page redirect - attempting login...")

                        login_success = await handle_facebook_login_redirect(page, url)

                        if login_success:
                            logger.info("Facebook login successful, proceeding with popup handling")
                            current_url = page.url
                        else:
                            logger.warning("Facebook login failed, proceeding with screenshot anyway")

                    # Enhanced cookie consent handling
                    cookie_buttons = page.locator(
                        'button[data-cookiebanner="accept_button"], '
                        'button[data-testid="cookie-policy-manage-dialog-accept-button"], '
                        'button:has-text("Accept All"), '
                        'button:has-text("Accept all"), '
                        'button:has-text("Allow all cookies"), '
                        'button:has-text("Accept"), '
                        '[role="button"]:has-text("Accept")'
                    )
                    if await cookie_buttons.count() > 0:
                        await cookie_buttons.first.click()
                        logger.info("Clicked Facebook cookie consent button")
                        await page.wait_for_timeout(500)

                    # Enhanced login dialog closing (for any remaining dialogs)
                    login_close_selectors = [
                        '[aria-label="Close"]',
                        'button[aria-label="Close"]',
                        'div[role="button"][aria-label="Close"]',
                        '[data-testid="close-button"]',
                        'button:has-text("×")',
                        'button:has-text("✕")',
                        '.modal-close'
                    ]

                    for selector in login_close_selectors:
                        try:
                            close_btn = page.locator(selector)
                            if await close_btn.count() > 0:
                                await close_btn.first.click()
                                logger.info(f"Closed Facebook dialog with selector: {selector}")
                                await page.wait_for_timeout(500)
                                break
                        except Exception as e:
                            logger.debug(f"Facebook close selector {selector} failed: {str(e)}")
                            continue

                    # Handle any remaining Facebook-specific popups
                    facebook_popup_selectors = [
                        'button:has-text("Not Now")',
                        'button:has-text("Skip")',
                        'button:has-text("Maybe Later")',
                        '[data-testid="cookie-policy-dialog-accept-button"]'
                    ]

                    for selector in facebook_popup_selectors:
                        try:
                            popup_btn = page.locator(selector)
                            if await popup_btn.count() > 0:
                                await popup_btn.first.click()
                                logger.info(f"Dismissed Facebook popup with selector: {selector}")
                                await page.wait_for_timeout(500)
                                break
                        except Exception as e:
                            logger.debug(f"Facebook popup selector {selector} failed: {str(e)}")
                            continue

                except Exception as e:
                    logger.warning(f"Error handling Facebook popup/login: {str(e)}")
            
            elif any(x_domain in url for x_domain in ["twitter.com", "x.com"]):
                logger.info("Handling X/Twitter-specific popups with enhanced timing...")

                try:
                    # Wait for X/Twitter popups to load
                    await page.wait_for_timeout(2500)

                    # Enhanced login wall popup handling
                    login_dialog_selectors = [
                        'div[aria-modal="true"] div[role="button"][data-testid="close"]',
                        'div[aria-modal="true"] button[aria-label="Close"]',
                        '[data-testid="app-bar-close"]',
                        '[data-testid="modal-header"] button',
                        'div[role="dialog"] button[aria-label="Close"]'
                    ]

                    for selector in login_dialog_selectors:
                        try:
                            close_btn = page.locator(selector)
                            if await close_btn.count() > 0:
                                await close_btn.first.click()
                                logger.info(f"Closed X/Twitter login dialog with selector: {selector}")
                                await page.wait_for_timeout(500)
                                break
                        except Exception as e:
                            logger.debug(f"X/Twitter close selector {selector} failed: {str(e)}")
                            continue

                    # Enhanced cookie consent handling
                    cookie_selectors = [
                        'div[role="dialog"] button:has-text("Accept")',
                        'button:has-text("Accept all cookies")',
                        'button:has-text("Accept All")',
                        '[data-testid="cookie-accept-all"]',
                        '[role="button"]:has-text("Accept")'
                    ]

                    for selector in cookie_selectors:
                        try:
                            cookie_btn = page.locator(selector)
                            if await cookie_btn.count() > 0:
                                await cookie_btn.first.click()
                                logger.info(f"Clicked X/Twitter cookie button with selector: {selector}")
                                await page.wait_for_timeout(500)
                                break
                        except Exception as e:
                            logger.debug(f"X/Twitter cookie selector {selector} failed: {str(e)}")
                            continue

                except Exception as e:
                    logger.warning(f"Error handling X/Twitter popup: {str(e)}")
            
            elif "linkedin.com" in url:
                logger.info("Handling LinkedIn-specific popups with enhanced timing...")

                try:
                    # Wait for LinkedIn popups to load
                    await page.wait_for_timeout(2000)

                    # Enhanced signin popup dismissal
                    dismiss_selectors = [
                        'button.artdeco-modal__dismiss',
                        'button[aria-label="Dismiss"]',
                        'button[aria-label="Close"]',
                        '[data-test-modal-close-btn]',
                        '.modal-close',
                        'button:has-text("×")',
                        'button:has-text("✕")'
                    ]

                    for selector in dismiss_selectors:
                        try:
                            dismiss_btn = page.locator(selector)
                            if await dismiss_btn.count() > 0:
                                await dismiss_btn.first.click()
                                logger.info(f"Dismissed LinkedIn popup with selector: {selector}")
                                await page.wait_for_timeout(500)
                                break
                        except Exception as e:
                            logger.debug(f"LinkedIn dismiss selector {selector} failed: {str(e)}")
                            continue

                    # Enhanced cookie consent handling
                    cookie_selectors = [
                        'button[action-type="DENY"]',
                        'button[data-control-name="ga-cookie.consent.reject.v4"]',
                        'button:has-text("Reject")',
                        'button:has-text("Decline")',
                        'button:has-text("Accept")',  # Sometimes accept is the only option
                        '[data-test-id="cookie-reject-all"]'
                    ]

                    for selector in cookie_selectors:
                        try:
                            cookie_btn = page.locator(selector)
                            if await cookie_btn.count() > 0:
                                await cookie_btn.first.click()
                                logger.info(f"Handled LinkedIn cookies with selector: {selector}")
                                await page.wait_for_timeout(500)
                                break
                        except Exception as e:
                            logger.debug(f"LinkedIn cookie selector {selector} failed: {str(e)}")
                            continue

                    # Enhanced login wall handling
                    try:
                        # Try multiple scroll positions to see content behind login wall
                        scroll_positions = [500, 1000, 300]
                        for scroll_y in scroll_positions:
                            await page.mouse.wheel(0, scroll_y)
                            await page.wait_for_timeout(200)
                        logger.info("Scrolled to potentially see content behind LinkedIn login wall")
                    except Exception as e:
                        logger.debug(f"LinkedIn scroll failed: {str(e)}")
                        pass

                except Exception as e:
                    logger.warning(f"Error handling LinkedIn popup: {str(e)}")
            
            # Enhanced generic popup handling for all platforms
            try:
                logger.info("Starting enhanced generic popup handling...")

                # Method 1: Enhanced dialog detection and closing
                dialog_selectors = [
                    'div[role="dialog"]',
                    'div[role="alertdialog"]',
                    '.modal',
                    '.popup',
                    '.overlay',
                    '[data-modal="true"]',
                    '.dialog'
                ]

                found_dialogs = False
                for dialog_selector in dialog_selectors:
                    dialog = page.locator(dialog_selector)
                    if await dialog.count() > 0:
                        found_dialogs = True
                        logger.info(f"Found {await dialog.count()} dialog(s) with selector: {dialog_selector}")
                        break

                if found_dialogs:
                    # Enhanced close button patterns
                    close_buttons = [
                        # SVG close buttons (common in modern sites)
                        'svg[aria-label="Close"], svg[aria-label="close"], svg[aria-label="Dismiss"]',
                        # X text buttons with various formats
                        'button:has-text("✕"), button:has-text("×"), button:has-text("X")',
                        'button:has-text("⨯"), button:has-text("✖")',
                        # Close/Cancel text buttons
                        'button:has-text("Close"), button:has-text("Cancel"), button:has-text("Dismiss")',
                        'button:has-text("Skip"), button:has-text("Later"), button:has-text("Not now")',
                        # Aria labeled buttons
                        'button[aria-label="Close"], button[aria-label="Dismiss"], button[aria-label="Cancel"]',
                        'button[aria-label="Skip"], button[title="Close"]',
                        # Modal dismiss buttons with various class patterns
                        '.modal-close, .modal-dismiss, .close-button, .popup-close',
                        '.btn-close, .dialog-close, .overlay-close',
                        # Data attribute patterns
                        '[data-dismiss="modal"], [data-close="true"], [data-action="close"]'
                    ]

                    # Try each selector with better error handling
                    for selector in close_buttons:
                        try:
                            button = page.locator(selector)
                            if await button.count() > 0:
                                # Handle SVG close buttons specially
                                if 'svg[aria-label=' in selector:
                                    # For SVG icons, get the parent button
                                    parent_button = button.locator('xpath=ancestor::button').first
                                    if await parent_button.count() > 0:
                                        await parent_button.click()
                                    else:
                                        await button.first.click()
                                else:
                                    await button.first.click()
                                logger.info(f"Closed dialog with selector: {selector}")
                                await page.wait_for_timeout(wait_after_close)
                                break
                        except Exception as e:
                            logger.debug(f"Generic close selector {selector} failed: {str(e)}")
                            continue
                
                # Method 2: Enhanced backdrop/overlay clicking
                overlay_selectors = [
                    '.modal-backdrop', '.modal-overlay', '.overlay', '.dialog-overlay',
                    '.popup-backdrop', '.backdrop', '[data-backdrop="true"]',
                    '.modal-background', '.dialog-background'
                ]

                for overlay_selector in overlay_selectors:
                    try:
                        overlays = page.locator(overlay_selector)
                        if await overlays.count() > 0:
                            # Try multiple click positions on the backdrop
                            click_positions = [
                                {'x': 10, 'y': 10},
                                {'x': 50, 'y': 50},
                                {'x': 5, 'y': 5}
                            ]

                            for pos in click_positions:
                                try:
                                    await overlays.first.click(position=pos)
                                    logger.info(f"Clicked on {overlay_selector} backdrop at position {pos}")
                                    await page.wait_for_timeout(wait_after_close)
                                    break
                                except Exception as e:
                                    logger.debug(f"Backdrop click at {pos} failed: {str(e)}")
                                    continue
                            break
                    except Exception as e:
                        logger.debug(f"Overlay selector {overlay_selector} failed: {str(e)}")
                        continue

                # Method 3: Enhanced generic button scanning with better patterns
                generic_buttons = page.locator('button, a[role="button"], div[role="button"]')
                button_count = await generic_buttons.count()
                scan_limit = min(button_count, 8)  # Increased from 5 to 8 for better coverage

                logger.info(f"Scanning {scan_limit} buttons out of {button_count} total for close patterns...")

                for i in range(scan_limit):
                    try:
                        button = generic_buttons.nth(i)
                        button_text = (await button.text_content() or "").strip()
                        button_aria = await button.get_attribute("aria-label") or ""
                        button_title = await button.get_attribute("title") or ""
                        button_class = await button.get_attribute("class") or ""

                        # Enhanced close button detection patterns
                        close_text_patterns = ["close", "dismiss", "cancel", "skip", "later", "not now", "✕", "×", "x", "⨯", "✖"]
                        close_aria_patterns = ["close", "dismiss", "cancel", "skip"]
                        close_class_patterns = ["close", "dismiss", "cancel", "modal-close", "popup-close"]

                        # Check if this looks like a close button
                        is_close_button = (
                            any(pattern in button_text.lower() for pattern in close_text_patterns) or
                            any(pattern in button_aria.lower() for pattern in close_aria_patterns) or
                            any(pattern in button_title.lower() for pattern in close_aria_patterns) or
                            any(pattern in button_class.lower() for pattern in close_class_patterns)
                        )

                        if is_close_button:
                            await button.click()
                            logger.info(f"Clicked potential close button: text='{button_text}' aria='{button_aria}' title='{button_title}'")
                            await page.wait_for_timeout(wait_after_close)
                            break

                    except Exception as e:
                        logger.debug(f"Generic button {i} scan failed: {str(e)}")
                        continue

            except Exception as e:
                logger.warning(f"Error while trying enhanced generic popup handling: {str(e)}")

        # Enhanced image loading wait with better logging
        logger.info(f"Waiting {image_load_wait}ms for images to load...")
        await page.wait_for_timeout(image_load_wait)

        # Enhanced network settling for social media platforms
        url_lower = url.lower()
        social_platforms = ["instagram.com", "facebook.com", "twitter.com", "x.com", "linkedin.com", "youtube.com", "pinterest.com", "tiktok.com"]
        is_social_platform = any(platform in url_lower for platform in social_platforms)

        if is_social_platform:
            logger.info("Social media platform detected - waiting for network to settle...")
            try:
                # Wait for network to be mostly idle (allows some ongoing requests)
                await page.wait_for_load_state("networkidle", timeout=5000)
                logger.info("Network settled successfully for social media platform")
            except Exception as e:
                logger.info(f"Network settle timeout (expected for some platforms): {str(e)}")

        # Popup handling summary
        if close_popups:
            try:
                # Check if any popups/modals are still visible
                remaining_popups = await page.locator('div[role="dialog"], div[role="alertdialog"], .modal, .popup').count()
                if remaining_popups > 0:
                    logger.warning(f"Screenshot will include {remaining_popups} remaining popup(s) that couldn't be closed")
                else:
                    logger.info("All popups successfully handled - clean screenshot expected")
            except Exception as e:
                logger.debug(f"Error checking remaining popups: {str(e)}")

        # Enhanced content validation to detect blank pages
        try:
            # Check if page has meaningful content
            body_text = await page.evaluate("() => document.body.innerText")
            visible_images = await page.locator('img[src]:visible').count()
            visible_content = await page.locator('div:visible, p:visible, span:visible').count()

            if len(body_text.strip()) < 50 and visible_images < 2 and visible_content < 10:
                logger.warning("Page appears to have minimal content - possible blank page", {
                    "text_length": len(body_text.strip()),
                    "visible_images": visible_images,
                    "visible_content": visible_content,
                    "url": url
                })

                # For Instagram, try one more reload
                if "instagram.com" in url:
                    logger.info("Attempting page reload for Instagram blank page...")
                    await page.reload(wait_until="domcontentloaded", timeout=10000)
                    await page.wait_for_timeout(3000)

                    # Re-check content after reload
                    body_text_after = await page.evaluate("() => document.body.innerText")
                    if len(body_text_after.strip()) > len(body_text.strip()):
                        logger.info("Page reload improved content for Instagram")
                    else:
                        logger.warning("Page reload did not improve content for Instagram")
            else:
                logger.info("Page content validation passed", {
                    "text_length": len(body_text.strip()),
                    "visible_images": visible_images,
                    "visible_content": visible_content
                })
        except Exception as e:
            logger.warning(f"Content validation failed: {str(e)}")

        # Enhanced screenshot capture with better error handling
        logger.info("Taking optimized screenshot with enhanced error handling...")
        try:
            screenshot_bytes = await page.screenshot(
                timeout=15000,  # Increased from 10s to 15s for better reliability
                type='png',
                full_page=True
            )

            capture_time = time.time() - start_time
            logger.info(f"Screenshot captured successfully in {capture_time:.2f}s", {
                "size_bytes": len(screenshot_bytes),
                "size_kb": round(len(screenshot_bytes) / 1024, 2),
                "url": url,
                "popup_handling": "enabled" if close_popups else "disabled",
                "platform_type": "social_media" if is_social_platform else "regular_website"
            })

            return screenshot_bytes

        except Exception as screenshot_error:
            logger.error(f"Screenshot capture failed: {str(screenshot_error)}", {
                "url": url,
                "error_type": type(screenshot_error).__name__,
                "capture_time": time.time() - start_time
            })
            raise screenshot_error
        
    except Exception as e:
        error_type = type(e).__name__
        capture_time = time.time() - start_time

        logger.error(f"Error capturing screenshot: {str(e)}", {
            "url": url,
            "error_type": error_type,
            "capture_time": capture_time,
            "popup_handling": "enabled" if close_popups else "disabled"
        })

        # Enhanced Instagram fallback with better error handling
        if "instagram.com" in url:
            logger.info("Main method failed for Instagram, attempting enhanced fallback method...")
            try:
                if browser:
                    await browser.close()
                fallback_result = await capture_instagram_screenshot_fallback(playwright, url, timeout=30)
                if fallback_result:
                    logger.info("Instagram fallback method succeeded!", {
                        "fallback_size_bytes": len(fallback_result),
                        "total_time": time.time() - start_time
                    })
                    return fallback_result
                else:
                    logger.warning("Instagram fallback method also failed")
            except Exception as fallback_error:
                logger.error(f"Instagram fallback error: {str(fallback_error)}", {
                    "fallback_error_type": type(fallback_error).__name__,
                    "original_error": str(e)
                })

        return None
    finally:
        # Enhanced cleanup to prevent zombie processes with better logging
        cleanup_start = time.time()
        try:
            if browser:
                logger.debug("Starting browser cleanup process...")

                # Close all contexts first with enhanced error handling
                contexts = browser.contexts
                for i, context in enumerate(contexts):
                    try:
                        await context.close()
                        logger.debug(f"Closed context {i+1}/{len(contexts)}")
                    except Exception as e:
                        logger.warning(f"Error closing context {i+1}: {str(e)}")

                # Close the browser with timeout
                try:
                    await browser.close()
                    cleanup_time = time.time() - cleanup_start
                    logger.debug(f"Browser closed successfully in {cleanup_time:.2f}s")
                except Exception as close_error:
                    logger.warning(f"Error during normal browser close: {str(close_error)}")

                    # Force kill browser process if normal close fails
                    try:
                        if hasattr(browser, '_connection') and browser._connection:
                            await browser._connection.dispose()
                            logger.info("Force disposed browser connection")
                    except Exception as force_error:
                        logger.error(f"Error during force browser cleanup: {str(force_error)}")
            else:
                logger.debug("No browser to cleanup")

        except Exception as e:
            cleanup_time = time.time() - cleanup_start
            logger.error(f"Critical error during browser cleanup after {cleanup_time:.2f}s: {str(e)}", {
                "error_type": type(e).__name__,
                "url": url if 'url' in locals() else "unknown"
            })